#!/bin/bash

# Deadlock API Ingest - Linux Installation Script
# This script downloads and installs the deadlock-api-ingest application as a systemd service

set -euo pipefail

# Configuration
APP_NAME="deadlock-api-ingest"
SERVICE_NAME="deadlock-api-ingest"
INSTALL_DIR="/opt/deadlock-api-ingest"
BIN_DIR="/usr/local/bin"
EXECUTABLE_NAME="deadlock-api-ingest-ubuntu-latest"
FINAL_EXECUTABLE_NAME="deadlock-api-ingest"
GITHUB_REPO="deadlock-api/deadlock-api-ingest"
LOG_FILE="/tmp/deadlock-api-ingest-install.log"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to write to log and console
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_message="[$timestamp] [$level] $message"
    
    case "$level" in
        "ERROR")
            echo -e "${RED}$log_message${NC}" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}$log_message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}$log_message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}$log_message${NC}"
            ;;
        *)
            echo "$log_message"
            ;;
    esac
    
    echo "$log_message" >> "$LOG_FILE"
}

# Function to show desktop notification
show_notification() {
    local title="$1"
    local message="$2"
    local urgency="${3:-normal}"
    
    if command -v notify-send >/dev/null 2>&1; then
        notify-send --urgency="$urgency" --app-name="$APP_NAME" "$title" "$message" 2>/dev/null || true
    elif command -v zenity >/dev/null 2>&1; then
        zenity --info --title="$title" --text="$message" 2>/dev/null || true
    else
        log "WARN" "No notification system available (notify-send or zenity)"
    fi
}

# Function to check if running with root/sudo privileges
check_privileges() {
    if [[ $EUID -ne 0 ]]; then
        log "INFO" "This script requires root privileges. Attempting to restart with sudo..."
        
        # Check if sudo is available
        if ! command -v sudo >/dev/null 2>&1; then
            log "ERROR" "sudo is not available. Please run this script as root."
            exit 1
        fi
        
        # Restart with sudo
        exec sudo "$0" "$@"
    fi
    
    log "INFO" "Running with root privileges"
}

# Function to install required dependencies
install_dependencies() {
    log "INFO" "Installing required dependencies..."
    
    # Detect package manager and install dependencies
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update -qq
        apt-get install -y curl wget jq libpcap0.8 2>/dev/null || {
            log "WARN" "Some packages may not be available, continuing..."
        }
    elif command -v yum >/dev/null 2>&1; then
        yum install -y curl wget jq libpcap 2>/dev/null || {
            log "WARN" "Some packages may not be available, continuing..."
        }
    elif command -v dnf >/dev/null 2>&1; then
        dnf install -y curl wget jq libpcap 2>/dev/null || {
            log "WARN" "Some packages may not be available, continuing..."
        }
    elif command -v pacman >/dev/null 2>&1; then
        pacman -Sy --noconfirm curl wget jq libpcap 2>/dev/null || {
            log "WARN" "Some packages may not be available, continuing..."
        }
    else
        log "WARN" "Unknown package manager. Please ensure curl, wget, jq, and libpcap are installed."
    fi
}

# Function to get latest release info from GitHub API
get_latest_release() {
    log "INFO" "Fetching latest release information from GitHub..."
    
    local api_url="https://api.github.com/repos/$GITHUB_REPO/releases/latest"
    local response
    
    if ! response=$(curl -s -f -H "User-Agent: Bash-Installer" "$api_url"); then
        log "ERROR" "Failed to fetch release information from GitHub API"
        exit 1
    fi
    
    # Parse JSON response
    if ! command -v jq >/dev/null 2>&1; then
        log "ERROR" "jq is required to parse JSON response. Please install jq."
        exit 1
    fi
    
    local version download_url size
    version=$(echo "$response" | jq -r '.tag_name')
    download_url=$(echo "$response" | jq -r ".assets[] | select(.name == \"$EXECUTABLE_NAME\") | .browser_download_url")
    size=$(echo "$response" | jq -r ".assets[] | select(.name == \"$EXECUTABLE_NAME\") | .size")
    
    if [[ "$download_url" == "null" || -z "$download_url" ]]; then
        log "ERROR" "Asset '$EXECUTABLE_NAME' not found in latest release"
        exit 1
    fi
    
    echo "$version|$download_url|$size"
}

# Function to download file with progress
download_file() {
    local url="$1"
    local output_path="$2"
    local expected_size="$3"
    
    log "INFO" "Downloading from: $url"
    log "INFO" "Saving to: $output_path"
    
    # Create directory if it doesn't exist
    mkdir -p "$(dirname "$output_path")"
    
    # Download with progress bar
    if ! wget --progress=bar:force --user-agent="Bash-Installer" -O "$output_path" "$url" 2>&1 | \
         sed -u 's/.* \([0-9]\+%\).*$/\1/' | \
         while read -r percent; do
             echo -ne "\rDownloading: $percent"
         done; then
        log "ERROR" "Download failed"
        exit 1
    fi
    
    echo # New line after progress
    log "SUCCESS" "Download completed successfully"
    
    # Verify file size
    local actual_size
    actual_size=$(stat -c%s "$output_path" 2>/dev/null || stat -f%z "$output_path" 2>/dev/null)
    
    if [[ "$actual_size" != "$expected_size" ]]; then
        log "ERROR" "File size mismatch. Expected: $expected_size, Actual: $actual_size"
        exit 1
    fi
    
    log "SUCCESS" "File integrity verified successfully"
}

# Function to stop and remove existing service
remove_existing_service() {
    if systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
        log "INFO" "Stopping existing service..."
        systemctl stop "$SERVICE_NAME" || true
    fi
    
    if systemctl is-enabled --quiet "$SERVICE_NAME" 2>/dev/null; then
        log "INFO" "Disabling existing service..."
        systemctl disable "$SERVICE_NAME" || true
    fi
    
    if [[ -f "$SYSTEMD_SERVICE_FILE" ]]; then
        log "INFO" "Removing existing service file..."
        rm -f "$SYSTEMD_SERVICE_FILE"
    fi
    
    # Reload systemd daemon
    systemctl daemon-reload
}

# Function to create systemd service
create_systemd_service() {
    local executable_path="$1"
    
    log "INFO" "Creating systemd service..."
    
    cat > "$SYSTEMD_SERVICE_FILE" << EOF
[Unit]
Description=Deadlock API Ingest Service
Documentation=https://github.com/$GITHUB_REPO
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=$executable_path
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR
PrivateTmp=true

# Resource limits
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF
    
    # Set proper permissions
    chmod 644 "$SYSTEMD_SERVICE_FILE"
    
    # Reload systemd daemon
    systemctl daemon-reload
    
    log "SUCCESS" "Systemd service created successfully"
}

# Function to enable and start service
start_service() {
    log "INFO" "Enabling service to start on boot..."
    systemctl enable "$SERVICE_NAME"
    
    log "INFO" "Starting service..."
    systemctl start "$SERVICE_NAME"
    
    # Wait a moment and check if service is running
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log "SUCCESS" "Service started successfully"
    else
        log "ERROR" "Service failed to start"
        log "INFO" "Service status:"
        systemctl status "$SERVICE_NAME" --no-pager || true
        log "INFO" "Service logs:"
        journalctl -u "$SERVICE_NAME" --no-pager -n 20 || true
        exit 1
    fi
}

# Main installation function
install_deadlock_api_ingest() {
    log "INFO" "Starting Deadlock API Ingest installation..."
    log "INFO" "Log file: $LOG_FILE"
    
    # Check privileges
    check_privileges "$@"
    
    # Install dependencies
    install_dependencies
    
    # Get latest release information
    local release_info version download_url size
    release_info=$(get_latest_release)
    IFS='|' read -r version download_url size <<< "$release_info"
    
    log "INFO" "Latest version: $version"
    
    # Create installation directory
    log "INFO" "Creating installation directory: $INSTALL_DIR"
    mkdir -p "$INSTALL_DIR"
    
    # Stop and remove existing service
    remove_existing_service
    
    # Download the executable
    local temp_file="$INSTALL_DIR/$EXECUTABLE_NAME"
    local final_path="$INSTALL_DIR/$FINAL_EXECUTABLE_NAME"
    local bin_symlink="$BIN_DIR/$FINAL_EXECUTABLE_NAME"
    
    download_file "$download_url" "$temp_file" "$size"
    
    # Move to final location and set permissions
    log "INFO" "Installing executable to: $final_path"
    mv "$temp_file" "$final_path"
    chmod +x "$final_path"
    
    # Create symlink in /usr/local/bin for easy access
    log "INFO" "Creating symlink: $bin_symlink"
    mkdir -p "$BIN_DIR"
    ln -sf "$final_path" "$bin_symlink"
    
    # Create and start service
    create_systemd_service "$final_path"
    start_service
    
    # Show success notification
    local success_message="Deadlock API Ingest $version installed successfully and is running as a service."
    show_notification "Installation Complete" "$success_message"
    log "SUCCESS" "$success_message"
    
    log "SUCCESS" "Installation completed successfully!"
    log "INFO" "Service Name: $SERVICE_NAME"
    log "INFO" "Installation Directory: $INSTALL_DIR"
    log "INFO" "Executable: $final_path"
    log "INFO" "Symlink: $bin_symlink"
    log "INFO" ""
    log "INFO" "You can manage the service using:"
    log "INFO" "  systemctl status $SERVICE_NAME"
    log "INFO" "  systemctl stop $SERVICE_NAME"
    log "INFO" "  systemctl start $SERVICE_NAME"
    log "INFO" "  systemctl restart $SERVICE_NAME"
    log "INFO" "  journalctl -u $SERVICE_NAME -f"
    
    # Auto-exit after 10 seconds
    log "INFO" "Script will exit automatically in 10 seconds..."
    for i in {10..1}; do
        echo -ne "\rExiting in $i seconds..."
        sleep 1
    done
    echo
}

# Error handling
trap 'log "ERROR" "Script failed at line $LINENO. Exit code: $?"' ERR

# Run the installation
install_deadlock_api_ingest "$@"
