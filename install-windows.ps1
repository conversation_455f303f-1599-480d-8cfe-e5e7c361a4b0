# Deadlock API Ingest - Windows Installation Script
# This script downloads and installs the deadlock-api-ingest application as a Windows service

param(
    [switch]$Force = $false
)

# Configuration
$AppName = "deadlock-api-ingest"
$ServiceName = "DeadlockAPIIngest"
$ServiceDisplayName = "Deadlock API Ingest Service"
$InstallDir = "C:\Program Files\deadlock-api-ingest"
$ExecutableName = "deadlock-api-ingest-windows-latest.exe"
$FinalExecutableName = "deadlock-api-ingest.exe"
$GitHubRepo = "deadlock-api/deadlock-api-ingest"
$LogFile = "$env:TEMP\deadlock-api-ingest-install.log"

# Function to write to log and console
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    Add-Content -Path $LogFile -Value $logMessage
}

# Function to show Windows toast notification
function Show-ToastNotification {
    param([string]$Title, [string]$Message)
    try {
        [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType = WindowsRuntime] | Out-Null
        [Windows.Data.Xml.Dom.XmlDocument, Windows.Data.Xml.Dom.XmlDocument, ContentType = WindowsRuntime] | Out-Null

        $template = @"
<toast>
    <visual>
        <binding template="ToastText02">
            <text id="1">$Title</text>
            <text id="2">$Message</text>
        </binding>
    </visual>
</toast>
"@

        $xml = New-Object Windows.Data.Xml.Dom.XmlDocument
        $xml.LoadXml($template)
        $toast = [Windows.UI.Notifications.ToastNotification]::new($xml)
        [Windows.UI.Notifications.ToastNotificationManager]::CreateToastNotifier($AppName).Show($toast)
    } catch {
        Write-Log "Failed to show toast notification: $($_.Exception.Message)" "WARN"
    }
}

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to restart script with administrator privileges
function Restart-AsAdministrator {
    Write-Log "Requesting administrator privileges..."
    $arguments = "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`""
    if ($Force) { $arguments += " -Force" }

    try {
        Start-Process PowerShell -Verb RunAs -ArgumentList $arguments -Wait
        exit 0
    } catch {
        Write-Log "Failed to restart with administrator privileges: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Function to get latest release info from GitHub API
function Get-LatestRelease {
    try {
        Write-Log "Fetching latest release information from GitHub..."
        $apiUrl = "https://api.github.com/repos/$GitHubRepo/releases/latest"
        $response = Invoke-RestMethod -Uri $apiUrl -Headers @{"User-Agent" = "PowerShell-Installer"}

        $asset = $response.assets | Where-Object { $_.name -eq $ExecutableName }
        if (-not $asset) {
            throw "Asset '$ExecutableName' not found in latest release"
        }

        return @{
            Version = $response.tag_name
            DownloadUrl = $asset.browser_download_url
            Size = $asset.size
        }
    } catch {
        Write-Log "Failed to fetch release information: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to download file with progress
function Download-File {
    param([string]$Url, [string]$OutputPath)

    try {
        Write-Log "Downloading from: $Url"
        Write-Log "Saving to: $OutputPath"

        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "PowerShell-Installer")

        # Register progress event
        Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
            $percent = $Event.SourceEventArgs.ProgressPercentage
            Write-Progress -Activity "Downloading $ExecutableName" -Status "$percent% Complete" -PercentComplete $percent
        } | Out-Null

        $webClient.DownloadFile($Url, $OutputPath)
        $webClient.Dispose()
        Write-Progress -Activity "Downloading $ExecutableName" -Completed

        Write-Log "Download completed successfully"
    } catch {
        Write-Log "Download failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to verify file integrity
function Test-FileIntegrity {
    param([string]$FilePath, [long]$ExpectedSize)

    if (-not (Test-Path $FilePath)) {
        throw "Downloaded file not found: $FilePath"
    }

    $actualSize = (Get-Item $FilePath).Length
    if ($actualSize -ne $ExpectedSize) {
        throw "File size mismatch. Expected: $ExpectedSize, Actual: $actualSize"
    }

    Write-Log "File integrity verified successfully"
}

# Function to stop and remove existing service
function Remove-ExistingService {
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            Write-Log "Stopping existing service..."
            Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue

            Write-Log "Removing existing service..."
            & sc.exe delete $ServiceName | Out-Null

            # Wait for service to be fully removed
            do {
                Start-Sleep -Seconds 1
                $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
            } while ($service)

            Write-Log "Existing service removed successfully"
        }
    } catch {
        Write-Log "Warning: Failed to remove existing service: $($_.Exception.Message)" "WARN"
    }
}

# Function to create Windows service
function Install-Service {
    param([string]$ExecutablePath)

    try {
        Write-Log "Creating Windows service..."

        $serviceArgs = @(
            "create"
            $ServiceName
            "binPath= `"$ExecutablePath`""
            "DisplayName= `"$ServiceDisplayName`""
            "start= auto"
            "type= own"
        )

        $result = & sc.exe @serviceArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create service. sc.exe exit code: $LASTEXITCODE"
        }

        # Set service description
        & sc.exe description $ServiceName "Monitors network traffic for Deadlock game replay files and ingests metadata to the API" | Out-Null

        # Configure service to restart on failure
        & sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/10000/restart/30000 | Out-Null

        Write-Log "Service created successfully"
    } catch {
        Write-Log "Failed to create service: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Function to start service
function Start-ServiceSafe {
    try {
        Write-Log "Starting service..."
        Start-Service -Name $ServiceName

        # Wait a moment and check if service is running
        Start-Sleep -Seconds 3
        $service = Get-Service -Name $ServiceName
        if ($service.Status -eq "Running") {
            Write-Log "Service started successfully"
        } else {
            throw "Service failed to start. Status: $($service.Status)"
        }
    } catch {
        Write-Log "Failed to start service: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Main installation function
function Install-DeadlockAPIIngest {
    try {
        Write-Log "Starting Deadlock API Ingest installation..."
        Write-Log "Log file: $LogFile"

        # Check administrator privileges
        if (-not (Test-Administrator)) {
            Restart-AsAdministrator
            return
        }

        Write-Log "Running with administrator privileges"

        # Get latest release information
        $release = Get-LatestRelease
        Write-Log "Latest version: $($release.Version)"

        # Create installation directory
        if (-not (Test-Path $InstallDir)) {
            Write-Log "Creating installation directory: $InstallDir"
            New-Item -ItemType Directory -Path $InstallDir -Force | Out-Null
        }

        # Stop and remove existing service if it exists
        Remove-ExistingService

        # Download the executable
        $tempFile = "$env:TEMP\$ExecutableName"
        $finalPath = "$InstallDir\$FinalExecutableName"

        Download-File -Url $release.DownloadUrl -OutputPath $tempFile
        Test-FileIntegrity -FilePath $tempFile -ExpectedSize $release.Size

        # Move to final location
        Write-Log "Installing executable to: $finalPath"
        Move-Item -Path $tempFile -Destination $finalPath -Force

        # Create and start service
        Install-Service -ExecutablePath $finalPath
        Start-ServiceSafe

        # Show success notification
        $successMessage = "Deadlock API Ingest $($release.Version) installed successfully and is running as a service."
        Show-ToastNotification -Title "Installation Complete" -Message $successMessage
        Write-Log $successMessage

        Write-Log "Installation completed successfully!"
        Write-Log "Service Name: $ServiceName"
        Write-Log "Installation Directory: $InstallDir"
        Write-Log "You can manage the service using Windows Services (services.msc)"

        # Auto-exit after 10 seconds
        Write-Log "Script will exit automatically in 10 seconds..."
        for ($i = 10; $i -gt 0; $i--) {
            Write-Host "Exiting in $i seconds..." -NoNewline
            Start-Sleep -Seconds 1
            Write-Host "`r" -NoNewline
        }
        Write-Host ""

    } catch {
        $errorMessage = "Installation failed: $($_.Exception.Message)"
        Write-Log $errorMessage "ERROR"
        Show-ToastNotification -Title "Installation Failed" -Message $errorMessage

        Write-Log "Press any key to exit..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit 1
    }
}

# Run the installation
Install-DeadlockAPIIngest