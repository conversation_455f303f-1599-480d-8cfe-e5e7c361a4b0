# Deadlock API Ingest

A network packet capture tool that monitors HTTP traffic for Deadlock game replay files and ingests metadata to the Deadlock API.

## Quick Installation

### Windows (PowerShell)

Run this command in an **elevated PowerShell** (Run as Administrator):

```powershell
irm https://raw.githubusercontent.com/deadlock-api/deadlock-api-ingest/master/install-windows.ps1 | iex
```

Or download and run manually:

```powershell
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/deadlock-api/deadlock-api-ingest/master/install-windows.ps1" -OutFile "install-windows.ps1"
.\install-windows.ps1
```

### Linux (Bash)

Run this command with **sudo privileges**:

```bash
curl -fsSL https://raw.githubusercontent.com/deadlock-api/deadlock-api-ingest/master/install-linux.sh | sudo bash
```

Or download and run manually:

```bash
wget https://raw.githubusercontent.com/deadlock-api/deadlock-api-ingest/master/install-linux.sh
chmod +x install-linux.sh
sudo ./install-linux.sh
```

## Uninstallation

### Windows
```powershell
# Stop and remove service
Stop-Service DeadlockAPIIngest
sc.exe delete DeadlockAPIIngest

# Remove installation directory
Remove-Item "C:\Program Files\deadlock-api-ingest" -Recurse -Force
```

### Linux
```bash
# Stop and disable service
sudo systemctl stop deadlock-api-ingest
sudo systemctl disable deadlock-api-ingest

# Remove service file
sudo rm /etc/systemd/system/deadlock-api-ingest.service
sudo systemctl daemon-reload

# Remove installation
sudo rm -rf /opt/deadlock-api-ingest
sudo rm /usr/local/bin/deadlock-api-ingest
```

## Privacy & Security

- **Local Processing**: All packet analysis is performed locally on your machine
- **Minimal Data**: Only extracts match metadata (IDs and salts) from replay URLs
- **No Personal Data**: Does not capture, store, or transmit personal information
- **Open Source**: Full source code is available for review

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues, please open an issue on the [GitHub repository](https://github.com/deadlock-api/deadlock-api-ingest/issues)
